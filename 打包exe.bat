@echo off
chcp 65001 >nul
title 文件解锁工具 - 打包exe

echo.
echo ========================================
echo      文件解锁工具 - 打包exe
echo ========================================
echo.

REM 检查Python环境
python --version >nul 2>&1
if errorlevel 1 (
    echo ❌ 错误：未找到Python环境
    echo 请先安装Python 3.6或更高版本
    pause
    exit /b 1
)

echo ✅ Python环境检查通过

REM 检查主程序文件
if not exist "file_unlocker_simple.py" (
    echo ❌ 错误：未找到主程序文件
    echo 请确保file_unlocker_simple.py在当前目录
    pause
    exit /b 1
)

echo ✅ 主程序文件检查通过

REM 执行打包脚本
echo.
echo 🚀 开始打包...
python build_exe.py

if errorlevel 1 (
    echo.
    echo ❌ 打包失败
    pause
    exit /b 1
)

echo.
echo 🎉 打包完成！
echo 📁 exe文件位置: dist\文件解锁工具.exe
echo.
pause
