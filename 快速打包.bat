@echo off
chcp 65001 >nul
title 快速打包exe

echo 🚀 文件解锁工具 - 快速打包
echo ================================

REM 安装PyInstaller
echo 📦 安装PyInstaller...
pip install pyinstaller

REM 创建图标
echo 🎨 创建应用图标...
python create_icon.py

REM 打包exe
echo 🔨 开始打包...
if exist "app_icon.ico" (
    pyinstaller --onefile --windowed --name="文件解锁工具" --icon="app_icon.ico" file_unlocker_simple.py
) else (
    pyinstaller --onefile --windowed --name="文件解锁工具" file_unlocker_simple.py
)

echo ✅ 打包完成！
echo 📁 exe文件位置: dist\文件解锁工具.exe

pause
