#!/usr/bin/env python3
"""
文件解锁工具打包脚本
使用PyInstaller打包为exe文件
"""

import os
import sys
import subprocess
import shutil


def check_pyinstaller():
    """检查PyInstaller是否安装"""
    try:
        import PyInstaller
        print("✅ PyInstaller已安装")
        return True
    except ImportError:
        print("❌ PyInstaller未安装")
        return False


def install_pyinstaller():
    """安装PyInstaller"""
    print("📦 正在安装PyInstaller...")
    try:
        subprocess.check_call([sys.executable, "-m", "pip", "install", "pyinstaller"])
        print("✅ PyInstaller安装成功")
        return True
    except subprocess.CalledProcessError:
        print("❌ PyInstaller安装失败")
        return False


def create_icon():
    """创建应用图标（可选）"""
    # 这里可以添加图标创建逻辑
    # 暂时返回None，表示不使用图标
    return None


def build_exe():
    """构建exe文件"""
    print("🔨 开始构建exe文件...")
    
    # 构建命令
    cmd = [
        "pyinstaller",
        "--onefile",                    # 打包成单个exe文件
        "--windowed",                   # 隐藏控制台窗口
        "--name=文件解锁工具",           # 指定exe文件名
        "--distpath=dist",              # 输出目录
        "--workpath=build",             # 临时文件目录
        "--specpath=.",                 # spec文件位置
        "--clean",                      # 清理临时文件
        "file_unlocker_simple.py"       # 主程序文件
    ]
    
    # 添加图标（如果有）
    icon_path = create_icon()
    if icon_path and os.path.exists(icon_path):
        cmd.extend(["--icon", icon_path])
    
    # 添加额外文件
    cmd.extend([
        "--add-data", "README.md;.",
        "--add-data", "requirements.txt;."
    ])
    
    try:
        # 执行构建
        subprocess.check_call(cmd)
        print("✅ exe文件构建成功！")
        
        # 检查输出文件
        exe_path = os.path.join("dist", "文件解锁工具.exe")
        if os.path.exists(exe_path):
            file_size = os.path.getsize(exe_path) / (1024 * 1024)  # MB
            print(f"📁 输出文件: {exe_path}")
            print(f"📊 文件大小: {file_size:.2f} MB")
            return True
        else:
            print("❌ 未找到输出的exe文件")
            return False
            
    except subprocess.CalledProcessError as e:
        print(f"❌ 构建失败: {e}")
        return False


def clean_build_files():
    """清理构建文件"""
    print("🧹 清理构建文件...")
    
    # 要清理的目录和文件
    cleanup_items = [
        "build",
        "__pycache__",
        "文件解锁工具.spec"
    ]
    
    for item in cleanup_items:
        if os.path.exists(item):
            if os.path.isdir(item):
                shutil.rmtree(item)
                print(f"🗑️ 删除目录: {item}")
            else:
                os.remove(item)
                print(f"🗑️ 删除文件: {item}")


def main():
    """主函数"""
    print("=" * 60)
    print("文件解锁工具 - exe打包脚本")
    print("=" * 60)
    
    # 检查PyInstaller
    if not check_pyinstaller():
        if not install_pyinstaller():
            print("❌ 无法安装PyInstaller，打包失败")
            return False
    
    # 检查主程序文件
    if not os.path.exists("file_unlocker_simple.py"):
        print("❌ 未找到主程序文件: file_unlocker_simple.py")
        return False
    
    # 构建exe
    if build_exe():
        print("\n" + "=" * 60)
        print("🎉 打包完成！")
        print("📁 exe文件位置: dist/文件解锁工具.exe")
        print("💡 可以将exe文件复制到任何地方使用")
        print("=" * 60)
        
        # 询问是否清理构建文件
        try:
            choice = input("\n是否清理构建文件？(y/n): ").strip().lower()
            if choice == 'y':
                clean_build_files()
                print("✅ 构建文件清理完成")
        except KeyboardInterrupt:
            print("\n操作被取消")
        
        return True
    else:
        print("❌ 打包失败")
        return False


if __name__ == "__main__":
    try:
        success = main()
        if not success:
            sys.exit(1)
    except KeyboardInterrupt:
        print("\n\n操作被用户取消")
        sys.exit(1)
    except Exception as e:
        print(f"\n❌ 发生错误: {e}")
        sys.exit(1)
