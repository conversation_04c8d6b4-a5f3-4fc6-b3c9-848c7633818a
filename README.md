# 线报链接处理器 AstrBot 插件

这是一个适用于 AstrBot 的线报链接处理插件，可以自动处理各种线报链接并生成相应的推广链接或解析结果。

## 功能特性

- 🔗 **京东链接处理**: 自动将京东商品链接转换为推广短链接
- 🎫 **Prodev链接解析**: 解析京东Prodev活动页面，提取优惠券信息和API地址
- 💬 **口令解析**: 解析京东口令，获取商品标题和链接
- 🛒 **淘宝转链**: 支持淘宝商品链接转推广链接
- 🏷️ **淘口令处理**: 解析和转换淘口令为推广链接
- 🔗 **短链接解析**: 解析3.cn、tb.cn等短链接，获取真实地址
- ⚙️ **配置管理**: 支持通过AstrBot管理面板进行配置
- 🎛️ **开关控制**: 可以通过指令启用/禁用处理功能

## 支持的链接类型

1. **京东商品链接**
   - `item.jd.com`
   - `item.m.jd.com`
   - `u.jd.com`
   - `jingfen.jd.com`

2. **Prodev活动链接**
   - `prodev.m.jd.com`
   - `pro.m.jd.com`

3. **京东口令**
   - 格式: `数字:/￥...￥`

4. **淘宝商品链接**
   - `item.taobao.com`
   - `detail.tmall.com`
   - `chaoshi.detail.tmall.com`
   - `detail.tmall.hk`

5. **淘宝短链接**
   - `m.tb.cn`
   - `tb.cn`

6. **淘口令**
   - 格式: `￥...￥`

7. **短链接**
   - `3.cn` 短链接

## 安装方法

1. 将插件文件放置到 AstrBot 的插件目录中
2. 重启 AstrBot 或在管理面板中重载插件
3. 在管理面板中配置插件参数（可选）

## 配置说明

插件支持以下配置项：

### 京东API配置
- `app_key`: 京东联盟APP KEY
- `secret_key`: 京东联盟SECRET KEY
- `union_id`: 联盟ID
- `union_id2`: 备用联盟ID
- `position_id`: 推广位ID

### 淘宝联盟API配置
- `app_key`: 淘宝联盟APP KEY
- `app_secret`: 淘宝联盟APP SECRET
- `adzone_id`: 推广位ID
- `sub_pid`: 三方分成PID（可选）
- `enable_taobao_convert`: 是否启用淘宝商品链接转换
- `enable_taobao_tpwd`: 是否启用淘口令处理

### 口令API配置
- `url`: 口令解析API地址

### 功能开关
- `enable_processing`: 是否启用自动处理功能

## 使用方法

### 自动处理
插件会自动监听所有消息，当检测到支持的链接类型时，会自动进行处理并回复处理结果。

### 手动控制指令

- `/xianbao status` - 查看当前状态
- `/xianbao enable` - 启用处理功能  
- `/xianbao disable` - 禁用处理功能
- `/xianbao help` - 显示帮助信息

## 处理示例

### 京东链接处理
输入: `https://item.jd.com/12345678.html`
输出: `https://u.jd.com/shortlink`

### Prodev链接处理
输入: `https://prodev.m.jd.com/mall/active/xxx/index.html`
输出: 
```
#1
优惠券名称: 满100-10
可用范围: 全品类
活动有效期: 2024-12-31
API 地址为:
https://api.m.jd.com/client.action?functionId=...
```

### 口令处理
输入: `1:/￥ABC123￥`
输出:
```
商品标题
https://item.jd.com/...
```

### 淘宝链接处理
输入: `https://item.taobao.com/item.htm?id=12345678`
输出: `https://s.click.taobao.com/t?e=xxx`

### 淘口令处理
输入: `￥ABC123￥`
输出: `https://s.click.taobao.com/t?e=xxx`

## 注意事项

1. 需要有效的京东联盟账号和API密钥
2. 部分功能需要网络连接
3. 处理大量链接时可能会有延迟
4. 建议定期检查API配置的有效性

## 更新日志

### v1.1.0
- 新增淘宝联盟转链功能
- 支持淘宝商品链接转推广链接
- 支持淘口令解析和转换
- 支持淘宝短链接处理
- 添加淘宝联盟API配置选项

### v1.0.0
- 初始版本
- 支持京东链接、Prodev链接、口令和短链接处理
- 去除Telegram推送功能，适配AstrBot
- 添加配置管理和开关控制功能

## 技术支持

如有问题或建议，请联系插件作者或在相关社区提出。
