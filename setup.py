"""
文件解锁工具打包脚本
使用cx_Freeze打包为exe文件
"""

import sys
from cx_Freeze import setup, Executable

# 应用信息
APP_NAME = "文件解锁工具"
APP_VERSION = "2.0"
APP_DESCRIPTION = "一个简洁易用的文件解锁工具，帮助解决文件被占用的问题"
APP_AUTHOR = "FileUnlocker"

# 包含的文件
include_files = [
    "README.md",
    "requirements.txt"
]

# 排除的模块
excludes = [
    "tkinter.test",
    "unittest",
    "email",
    "http",
    "urllib",
    "xml",
    "pydoc",
    "doctest",
    "argparse"
]

# 包含的包
packages = [
    "tkinter",
    "psutil",
    "os",
    "sys",
    "threading",
    "datetime"
]

# 构建选项
build_exe_options = {
    "packages": packages,
    "excludes": excludes,
    "include_files": include_files,
    "optimize": 2,
    "include_msvcrt": True
}

# 可执行文件配置
base = None
if sys.platform == "win32":
    base = "Win32GUI"  # 隐藏控制台窗口

executable = Executable(
    script="file_unlocker_simple.py",
    base=base,
    target_name="文件解锁工具.exe",
    icon=None  # 可以添加图标文件路径
)

# 设置配置
setup(
    name=APP_NAME,
    version=APP_VERSION,
    description=APP_DESCRIPTION,
    author=APP_AUTHOR,
    options={"build_exe": build_exe_options},
    executables=[executable]
)
