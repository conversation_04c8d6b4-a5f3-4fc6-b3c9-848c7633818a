"""
简化版文件解锁工具 - 简洁UI，无需额外依赖
"""

import tkinter as tk
from tkinter import ttk, filedialog, messagebox
import threading
import os
import psutil
from datetime import datetime


class FileUnlocker:
    """文件解锁器核心类"""
    
    def find_processes_using_file(self, file_path):
        """查找占用文件/文件夹的进程（优化版）"""
        processes = []

        if not os.path.exists(file_path):
            return processes

        abs_file_path = os.path.abspath(file_path)
        is_directory = os.path.isdir(abs_file_path)

        try:
            # 使用更高效的进程迭代
            for proc in psutil.process_iter(['pid', 'name', 'cwd'], ad_value=None):
                try:
                    # 跳过无效进程
                    if proc.info['pid'] is None or proc.info['name'] is None:
                        continue

                    # 检查工作目录（对文件夹特别有用）
                    if is_directory and proc.info['cwd']:
                        if proc.info['cwd'].startswith(abs_file_path):
                            processes.append({
                                'pid': proc.info['pid'],
                                'name': proc.info['name'],
                                'type': 'working_directory',
                                'process': proc
                            })
                            continue

                    # 检查打开的文件
                    try:
                        open_files = proc.open_files()
                        for file_info in open_files:
                            file_match = False

                            if is_directory:
                                # 对于文件夹，检查是否有文件在其中
                                if file_info.path.startswith(abs_file_path):
                                    file_match = True
                            else:
                                # 对于文件，精确匹配
                                if file_info.path == abs_file_path:
                                    file_match = True

                            if file_match:
                                processes.append({
                                    'pid': proc.info['pid'],
                                    'name': proc.info['name'],
                                    'type': 'open_file',
                                    'file_path': file_info.path,
                                    'process': proc
                                })
                                break
                    except (psutil.AccessDenied, AttributeError):
                        # 某些进程无法访问打开文件列表
                        continue

                except (psutil.NoSuchProcess, psutil.ZombieProcess):
                    continue
                except Exception:
                    continue
        except Exception:
            pass

        return processes
    
    def kill_process(self, pid, force=False):
        """结束进程"""
        try:
            proc = psutil.Process(pid)
            if force:
                proc.kill()
            else:
                proc.terminate()
            proc.wait(timeout=3)
            return True
        except:
            return False
    
    def unlock_file(self, file_path, force=False):
        """解锁文件"""
        processes = self.find_processes_using_file(file_path)
        if not processes:
            return True, "文件未被占用"
        
        success_count = 0
        for proc_info in processes:
            if self.kill_process(proc_info['pid'], force):
                success_count += 1
        
        if success_count == len(processes):
            return True, f"成功解锁，结束了 {success_count} 个进程"
        else:
            return False, f"部分成功，结束了 {success_count}/{len(processes)} 个进程"


class SimpleGUI:
    """简洁的图形界面"""
    
    def __init__(self):
        self.root = tk.Tk()
        self.root.title("文件解锁工具")
        self.root.geometry("650x550")

        # 设置天蓝色渐变主题
        self.setup_blue_gradient_theme()

        # 创建解锁器实例
        self.unlocker = FileUnlocker()

        # 当前文件路径
        self.current_file = ""

        # 创建界面
        self.create_widgets()

        # 设置拖拽功能
        self.setup_drag_drop()
    
    def setup_blue_gradient_theme(self):
        """设置天蓝色渐变主题"""
        # 天蓝色渐变色彩方案
        self.colors = {
            'bg_main': '#e3f2fd',           # 主背景 - 浅天蓝
            'bg_secondary': '#bbdefb',       # 次要背景 - 中天蓝
            'bg_accent': '#90caf9',          # 强调背景 - 深天蓝
            'bg_dark': '#42a5f5',            # 深色背景 - 蓝色
            'text_primary': '#0d47a1',       # 主要文字 - 深蓝
            'text_secondary': '#1565c0',     # 次要文字 - 中蓝
            'text_light': '#ffffff',         # 浅色文字 - 白色
            'border': '#64b5f6',             # 边框色 - 亮蓝
            'shadow': '#e1f5fe'              # 阴影色 - 极浅蓝
        }

        # 设置主窗口背景渐变效果
        self.root.configure(bg=self.colors['bg_main'])

        # 设置窗口透明度和模糊效果（Windows）
        try:
            self.root.wm_attributes('-alpha', 0.95)  # 轻微透明
        except:
            pass
    
    def create_widgets(self):
        """创建界面组件"""
        # 主容器 - 使用天蓝色渐变背景
        main_frame = tk.Frame(self.root, bg=self.colors['bg_main'])
        main_frame.pack(fill=tk.BOTH, expand=True, padx=20, pady=20)

        # 顶部大拖拽区域 - 白色背景带蓝色边框
        self.top_drop_frame = tk.Frame(
            main_frame,
            bg='#ffffff',
            relief='solid',
            bd=3,
            highlightbackground=self.colors['border'],
            highlightthickness=2,
            height=200
        )
        self.top_drop_frame.pack(fill=tk.X, pady=(0, 15))
        self.top_drop_frame.pack_propagate(False)

        # 顶部区域内容
        top_content = tk.Frame(self.top_drop_frame, bg='#ffffff')
        top_content.pack(expand=True, fill=tk.BOTH, padx=20, pady=20)

        # 应用Logo和标题
        logo_frame = tk.Frame(top_content, bg='#ffffff')
        logo_frame.pack(fill=tk.X, pady=(0, 10))

        # Logo图标
        logo_label = tk.Label(
            logo_frame,
            text="🔓",
            font=('Arial', 24),
            bg='#ffffff',
            fg=self.colors['bg_dark']
        )
        logo_label.pack(side=tk.LEFT)

        # 应用标题
        title_label = tk.Label(
            logo_frame,
            text="文件解锁工具",
            font=('Microsoft YaHei', 16, 'bold'),
            bg='#ffffff',
            fg=self.colors['text_primary']
        )
        title_label.pack(side=tk.LEFT, padx=(10, 0))

        # 版本信息
        version_label = tk.Label(
            logo_frame,
            text="v2.0",
            font=('Microsoft YaHei', 10),
            bg='#ffffff',
            fg=self.colors['text_secondary']
        )
        version_label.pack(side=tk.RIGHT)

        # 文件名显示 - 使用深蓝色文字
        self.file_name_var = tk.StringVar(value="请选择要解锁的文件")
        file_name_label = tk.Label(
            top_content,
            textvariable=self.file_name_var,
            font=('Microsoft YaHei', 12, 'bold'),
            bg='#ffffff',
            fg=self.colors['text_primary']
        )
        file_name_label.pack(anchor='w')

        # 解锁状态显示 - 使用中蓝色文字
        self.unlock_status_var = tk.StringVar(value="等待检查文件状态...")
        status_label = tk.Label(
            top_content,
            textvariable=self.unlock_status_var,
            font=('Microsoft YaHei', 10),
            bg='#ffffff',
            fg=self.colors['text_secondary']
        )
        status_label.pack(anchor='w', pady=(5, 0))

        # 操作提示 - 使用强调蓝色
        drag_hint_label = tk.Label(
            top_content,
            text="点击此处选择文件",
            font=('Microsoft YaHei', 11),
            bg='#ffffff',
            fg=self.colors['bg_dark'],
            cursor='hand2'
        )
        drag_hint_label.pack(anchor='e', side=tk.BOTTOM)
        drag_hint_label.bind('<Button-1>', self.browse_file)

        # 中间按钮区域 - 使用深蓝色背景
        button_frame = tk.Frame(
            main_frame,
            bg=self.colors['bg_dark'],
            relief='solid',
            bd=2,
            height=60
        )
        button_frame.pack(fill=tk.X, pady=(0, 15))
        button_frame.pack_propagate(False)

        # 按钮容器
        btn_container = tk.Frame(button_frame, bg=self.colors['bg_dark'])
        btn_container.pack(expand=True)

        # 各种按钮
        self.create_action_buttons(btn_container)

        # 设置底部区域
        self.setup_bottom_area(main_frame)

    def create_action_buttons(self, parent):
        """创建操作按钮"""
        # 移除检查按钮，因为已有自动检查功能

        # 解锁按钮 - 深蓝色
        unlock_btn = tk.Button(
            parent,
            text="🔓 解锁",
            font=('Microsoft YaHei', 10, 'bold'),
            bg=self.colors['bg_dark'],
            fg=self.colors['text_light'],
            relief='flat',
            padx=15,
            pady=8,
            cursor='hand2',
            command=self.unlock_file,
            activebackground=self.colors['bg_accent']
        )
        unlock_btn.pack(side=tk.LEFT, padx=5)

        # 强制解锁按钮 - 保持红色警告
        force_btn = tk.Button(
            parent,
            text="⚡ 强制",
            font=('Microsoft YaHei', 10, 'bold'),
            bg='#e74c3c',
            fg='white',
            relief='flat',
            padx=15,
            pady=8,
            cursor='hand2',
            command=self.force_unlock_file,
            activebackground='#c0392b'
        )
        force_btn.pack(side=tk.LEFT, padx=5)

        # 选择文件按钮 - 中蓝色
        select_btn = tk.Button(
            parent,
            text="📁 选择",
            font=('Microsoft YaHei', 10, 'bold'),
            bg=self.colors['bg_secondary'],
            fg=self.colors['text_primary'],
            relief='flat',
            padx=15,
            pady=8,
            cursor='hand2',
            command=self.browse_file,
            activebackground=self.colors['bg_accent']
        )
        select_btn.pack(side=tk.LEFT, padx=5)

        # 帮助按钮 - 浅蓝色
        help_btn = tk.Button(
            parent,
            text="❓ 帮助",
            font=('Microsoft YaHei', 10, 'bold'),
            bg=self.colors['shadow'],
            fg=self.colors['text_secondary'],
            relief='flat',
            padx=15,
            pady=8,
            cursor='hand2',
            command=self.show_help,
            activebackground=self.colors['bg_secondary']
        )
        help_btn.pack(side=tk.RIGHT, padx=5)

    def setup_bottom_area(self, main_frame):
        """设置底部区域"""
        # 底部拖拽区域
        self.bottom_drop_frame = tk.Frame(
            main_frame,
            bg='#e3f2fd',
            relief='groove',
            bd=2,
            height=120
        )
        self.bottom_drop_frame.pack(fill=tk.BOTH, expand=True)
        self.bottom_drop_frame.pack_propagate(False)

        # 底部区域内容 - 使用天蓝色背景
        bottom_content = tk.Frame(self.bottom_drop_frame, bg=self.colors['bg_main'])
        bottom_content.pack(expand=True, fill=tk.BOTH)

        # 进程列表标题 - 使用深蓝色文字
        process_title = tk.Label(
            bottom_content,
            text="占用进程列表",
            font=('Microsoft YaHei', 12, 'bold'),
            bg=self.colors['bg_main'],
            fg=self.colors['text_primary']
        )
        process_title.pack(pady=(10, 5))

        # 进程列表容器
        list_container = tk.Frame(bottom_content, bg=self.colors['bg_main'])
        list_container.pack(fill=tk.BOTH, expand=True, padx=20, pady=(0, 20))

        # 创建简化的进程列表 - 天蓝色主题
        self.process_listbox = tk.Listbox(
            list_container,
            font=('Consolas', 10),
            bg='#ffffff',
            fg=self.colors['text_primary'],
            selectbackground=self.colors['bg_accent'],
            selectforeground=self.colors['text_light'],
            relief='flat',
            bd=1,
            height=4
        )
        self.process_listbox.pack(fill=tk.BOTH, expand=True)

        # 提示信息区域 - 使用中蓝色文字
        suggestion_label = tk.Label(
            bottom_content,
            text="💡 提示：选择文件后点击检查按钮查看占用状态，支持拖拽文件到顶部区域",
            font=('Microsoft YaHei', 9),
            bg=self.colors['bg_main'],
            fg=self.colors['text_secondary']
        )
        suggestion_label.pack(side=tk.BOTTOM, pady=(0, 10))
    
    def browse_file(self, event=None):
        """智能选择文件或文件夹"""
        from tkinter import filedialog, messagebox

        # 询问用户要选择什么类型
        choice = messagebox.askyesno(
            "选择类型",
            "请选择要检查的类型：\n\n是 - 选择文件\n否 - 选择文件夹",
            icon='question'
        )

        if choice:  # 选择文件
            file_path = filedialog.askopenfilename(
                title="选择要检查的文件",
                filetypes=[
                    ("所有文件", "*.*"),
                    ("可执行文件", "*.exe;*.msi;*.bat;*.cmd"),
                    ("压缩文件", "*.zip;*.rar;*.7z;*.tar;*.gz"),
                    ("文档文件", "*.doc;*.docx;*.pdf;*.txt;*.xls;*.xlsx"),
                    ("图片文件", "*.jpg;*.jpeg;*.png;*.gif;*.bmp;*.tiff"),
                    ("视频文件", "*.mp4;*.avi;*.mkv;*.mov;*.wmv;*.flv"),
                    ("音频文件", "*.mp3;*.wav;*.flac;*.aac;*.ogg"),
                    ("数据库文件", "*.db;*.sqlite;*.mdb;*.accdb"),
                    ("日志文件", "*.log;*.txt")
                ]
            )
        else:  # 选择文件夹
            file_path = filedialog.askdirectory(
                title="选择要检查的文件夹"
            )

        if file_path:
            self.set_current_file(file_path)

    def set_current_file(self, file_path):
        """设置当前文件/文件夹并更新界面"""
        self.current_file = file_path
        filename = os.path.basename(file_path)

        # 根据类型显示不同图标
        if os.path.isdir(file_path):
            self.file_name_var.set(f"� {filename}")
            self.unlock_status_var.set("文件夹已选择，正在检查占用状态...")
        else:
            # 根据文件扩展名显示不同图标
            ext = os.path.splitext(filename)[1].lower()
            if ext in ['.exe', '.msi', '.bat', '.cmd']:
                icon = "⚙️"
            elif ext in ['.zip', '.rar', '.7z', '.tar', '.gz']:
                icon = "📦"
            elif ext in ['.doc', '.docx', '.pdf', '.txt', '.xls', '.xlsx']:
                icon = "📄"
            elif ext in ['.jpg', '.jpeg', '.png', '.gif', '.bmp']:
                icon = "🖼️"
            elif ext in ['.mp4', '.avi', '.mkv', '.mov', '.wmv']:
                icon = "🎬"
            elif ext in ['.mp3', '.wav', '.flac', '.aac']:
                icon = "🎵"
            else:
                icon = "�📄"

            self.file_name_var.set(f"{icon} {filename}")
            self.unlock_status_var.set("文件已选择，正在检查占用状态...")

        # 自动检查文件
        self.check_file()

    def setup_drag_drop(self):
        """设置拖拽功能"""
        try:
            # 尝试使用tkinterdnd2
            import tkinterdnd2
            # 如果成功导入，设置真正的拖拽功能
            self.setup_real_drag_drop()
        except ImportError:
            # 如果没有tkinterdnd2，使用点击模拟
            self.setup_click_simulation()

    def setup_real_drag_drop(self):
        """设置真正的拖拽功能（需要tkinterdnd2）"""
        try:
            # 为顶部区域绑定拖拽事件
            self.top_drop_frame.drop_target_register('DND_Files')
            self.top_drop_frame.dnd_bind('<<Drop>>', self.on_drop)
            self.top_drop_frame.dnd_bind('<<DragEnter>>', self.on_drag_enter)
            self.top_drop_frame.dnd_bind('<<DragLeave>>', self.on_drag_leave)

            # 更新提示文字
            self.update_drag_hint("拖拽文件到此处或点击选择")
        except:
            self.setup_click_simulation()

    def setup_click_simulation(self):
        """设置点击模拟拖拽"""
        # 绑定点击事件
        self.top_drop_frame.bind('<Button-1>', self.browse_file)
        self.top_drop_frame.bind('<Double-Button-1>', self.browse_file)

        # 添加视觉提示
        self.top_drop_frame.bind('<Enter>', self.on_drag_enter)
        self.top_drop_frame.bind('<Leave>', self.on_drag_leave)

        # 更新提示文字
        self.update_drag_hint("点击此处选择文件")

    def update_drag_hint(self, text):
        """更新拖拽提示文字"""
        try:
            # 查找并更新提示标签
            for widget in self.top_drop_frame.winfo_children():
                for child in widget.winfo_children():
                    if isinstance(child, tk.Label) and "点击" in child.cget("text"):
                        child.config(text=text)
                        break
        except:
            pass

    def on_drop(self, event):
        """处理文件拖拽"""
        try:
            # 获取拖拽的文件路径
            files = event.data.split()
            if files:
                file_path = files[0].strip('{}')  # 移除可能的大括号
                if os.path.exists(file_path):
                    self.set_current_file(file_path)
                else:
                    self.unlock_status_var.set("❌ 拖拽的文件不存在")
        except Exception as e:
            self.unlock_status_var.set(f"❌ 拖拽处理失败: {e}")

    def on_drag_enter(self, event=None):
        """拖拽进入时的视觉反馈"""
        self.top_drop_frame.configure(bg='#e8f4fd', relief='raised')

    def on_drag_leave(self, event=None):
        """拖拽离开时恢复样式"""
        self.top_drop_frame.configure(bg='#ffffff', relief='solid')
    
    def check_file(self):
        """检查文件占用状态"""
        if not self.current_file:
            messagebox.showwarning("提示", "请先选择一个文件或文件夹")
            return

        # 显示检查状态
        file_type = "文件夹" if os.path.isdir(self.current_file) else "文件"
        self.unlock_status_var.set(f"🔄 正在检查{file_type}占用状态...")

        # 清空进程列表并显示加载提示
        self.process_listbox.delete(0, tk.END)
        self.process_listbox.insert(0, "⏳ 正在扫描进程，请稍候...")

        # 后台检查
        threading.Thread(target=self._check_file_thread, daemon=True).start()
    
    def _check_file_thread(self):
        """后台检查文件线程"""
        try:
            processes = self.unlocker.find_processes_using_file(self.current_file)
            self.root.after(0, self._update_ui, processes)
        except Exception as e:
            self.root.after(0, self._show_error, f"检查失败: {e}")
    
    def _update_ui(self, processes):
        """更新界面显示"""
        # 清空进程列表
        self.process_listbox.delete(0, tk.END)

        if not processes:
            if os.path.isdir(self.current_file):
                self.unlock_status_var.set("✅ 文件夹未被占用，可以正常操作")
                self.process_listbox.insert(0, "没有进程占用此文件夹")
            else:
                self.unlock_status_var.set("✅ 文件未被占用，可以正常操作")
                self.process_listbox.insert(0, "没有进程占用此文件")
        else:
            file_type = "文件夹" if os.path.isdir(self.current_file) else "文件"
            self.unlock_status_var.set(f"⚠️ {file_type}被 {len(processes)} 个进程占用")

            for proc in processes:
                # 显示进程类型
                proc_type = proc.get('type', 'unknown')
                if proc_type == 'working_directory':
                    type_icon = "📂"
                    type_desc = "工作目录"
                elif proc_type == 'open_file':
                    type_icon = "📄"
                    type_desc = "打开文件"
                else:
                    type_icon = "❓"
                    type_desc = "未知"

                display_text = f"{type_icon} PID:{proc['pid']} - {proc['name']} ({type_desc})"
                self.process_listbox.insert(tk.END, display_text)
    
    def unlock_file(self):
        """正常解锁文件"""
        self._unlock_internal(force=False)
    
    def force_unlock_file(self):
        """强制解锁文件"""
        result = messagebox.askyesno(
            "确认操作", 
            "强制解锁将立即结束所有占用进程，可能导致数据丢失！\n\n确定要继续吗？",
            icon='warning'
        )
        if result:
            self._unlock_internal(force=True)
    
    def _unlock_internal(self, force=False):
        """内部解锁方法"""
        if not self.current_file:
            messagebox.showwarning("提示", "请先选择一个文件")
            return
        
        action = "强制解锁" if force else "解锁"
        self.unlock_status_var.set(f"🔄 正在{action}...")
        
        # 后台解锁
        threading.Thread(target=self._unlock_thread, args=(force,), daemon=True).start()
    
    def _unlock_thread(self, force):
        """后台解锁线程"""
        try:
            success, message = self.unlocker.unlock_file(self.current_file, force)
            self.root.after(0, self._show_result, success, message)
            self.root.after(1000, self.check_file)  # 1秒后重新检查
        except Exception as e:
            self.root.after(0, self._show_error, f"解锁失败: {e}")
    
    def _show_result(self, success, message):
        """显示操作结果"""
        if success:
            messagebox.showinfo("操作成功", message)
        else:
            messagebox.showwarning("操作完成", message)
    
    def _show_error(self, error_msg):
        """显示错误信息"""
        messagebox.showerror("错误", error_msg)
        self.unlock_status_var.set("❌ 操作失败")
    
    def show_help(self):
        """显示帮助信息"""
        help_text = """
文件解锁工具使用说明 v2.0

� 支持类型：
• 文件：所有类型的文件（exe、zip、doc、mp4等）
• 文件夹：目录及其子文件
• 压缩包：zip、rar、7z等压缩文件
• 数据库：db、sqlite等数据库文件

🔍 智能检查：
• 选择文件后自动检查占用状态
• 快速扫描占用进程
• 显示进程类型（📂工作目录 📄打开文件）
• 智能图标识别文件类型

🔓 解锁模式：
• 正常解锁：优雅结束进程（推荐）
• 强制解锁：立即终止进程（谨慎使用）

📱 操作方式：
• 点击"📁 选择"按钮智能选择文件/文件夹
• 点击顶部区域快速选择
• 支持拖拽文件到程序（如果支持）
• 选择后自动检查，无需手动点击检查

⚠️ 安全提醒：
• 结束进程前请保存重要数据
• 强制解锁可能导致数据丢失
• 某些系统进程需要管理员权限
• 文件夹解锁会影响其中所有文件

💡 使用技巧：
• 程序会自动识别文件类型并显示对应图标
• 进程列表显示占用类型（📂工作目录 📄打开文件）
• 支持同时检查多个占用进程
• 解锁后会自动重新检查状态
        """
        
        help_window = tk.Toplevel(self.root)
        help_window.title("使用帮助")
        help_window.geometry("400x500")
        help_window.configure(bg='#f5f6fa')
        help_window.resizable(False, False)
        
        # 居中显示
        help_window.transient(self.root)
        help_window.grab_set()
        
        text_widget = tk.Text(help_window, wrap=tk.WORD, font=('Microsoft YaHei', 10),
                             bg='#ffffff', fg='#2f3542', relief='flat', padx=20, pady=20)
        text_widget.pack(fill=tk.BOTH, expand=True, padx=20, pady=20)
        text_widget.insert(tk.END, help_text)
        text_widget.configure(state='disabled')
    
    def run(self):
        """运行程序"""
        # 设置窗口居中
        self.root.update_idletasks()
        x = (self.root.winfo_screenwidth() // 2) - (self.root.winfo_width() // 2)
        y = (self.root.winfo_screenheight() // 2) - (self.root.winfo_height() // 2)
        self.root.geometry(f"+{x}+{y}")
        
        self.root.mainloop()


def main():
    """主函数"""
    try:
        app = SimpleGUI()
        app.run()
    except Exception as e:
        print(f"程序启动失败: {e}")
        input("按回车键退出...")


if __name__ == "__main__":
    main()
