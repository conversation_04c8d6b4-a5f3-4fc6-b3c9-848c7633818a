@echo off
chcp 65001 >nul
title 文件解锁工具 - 简化版

echo.
echo ================================
echo     文件解锁工具 - 简化版
echo ================================
echo.

REM 检查Python是否安装
python --version >nul 2>&1
if errorlevel 1 (
    echo ❌ 错误：未找到Python
    echo 请先安装Python 3.6或更高版本
    echo 下载地址：https://www.python.org/downloads/
    echo.
    pause
    exit /b 1
)

echo ✅ Python环境检查通过

REM 检查psutil是否安装
python -c "import psutil" >nul 2>&1
if errorlevel 1 (
    echo.
    echo 📦 正在安装必要的依赖包...
    pip install psutil
    if errorlevel 1 (
        echo ❌ 安装依赖失败，请检查网络连接
        pause
        exit /b 1
    )
    echo ✅ 依赖安装完成
)

echo.
echo 🚀 正在启动文件解锁工具...
echo.

REM 启动简化版程序
python file_unlocker_simple.py

if errorlevel 1 (
    echo.
    echo ❌ 程序运行出错
    pause
)
